#ifndef __USART_H
#define __USART_H

#include "board.h"
#include "fifo.h"

/**********************************************************
*** ZDT_X42_V2.0步进闭环控制例程
*** 编写作者：ZHANGDATOU
*** 技术支持：张大头闭环伺服
*** 淘宝店铺：https://zhangdatou.taobao.com
*** CSDN博客：http s://blog.csdn.net/zhangdatou666
*** qq交流群：262438510
**********************************************************/

extern __IO bool rxFrameFlag;
extern __IO uint8_t u_rxCmd[FIFO_SIZE];
extern __IO uint8_t u_rxCount;

void usart_SendCmd(uint8_t *cmd, uint8_t len);
void usart_SendByte(uint16_t data);

#endif
