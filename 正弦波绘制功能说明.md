# 正弦波绘制功能说明

## 功能概述

在原有的云台控制系统基础上，新增了正弦波绘制功能。系统可以自动生成正弦波的坐标点，并控制云台按照正弦波轨迹进行连续运动。

## 新增功能

### 1. 正弦波坐标生成
- 函数：`vGenerate_Sine_Wave(int16_t amplitude, int16_t period, uint8_t points, int16_t start_x)`
- 功能：根据指定的振幅、周期和点数生成正弦波坐标
- 参数：
  - `amplitude`: 正弦波振幅（像素单位）
  - `period`: 正弦波周期（像素单位）
  - `points`: 生成的坐标点数量
  - `start_x`: 起始X坐标

### 2. 正弦波绘制
- 函数：`vDraw_Sine_Wave(int16_t amplitude, int16_t period, uint8_t cycles)`
- 功能：绘制指定参数的正弦波
- 参数：
  - `amplitude`: 振幅（像素）
  - `period`: 周期（像素）
  - `cycles`: 周期数

### 3. 测试函数
- 函数：`vTest_Sine_Wave(void)`
- 功能：测试不同参数的正弦波绘制

## 操作方式

### 按键控制
- **按键单击**：接收4个坐标点并运行（原功能）
- **按键双击**：绘制正弦波（新功能）
- **按键1双击**：回到原点0,0（原功能）
- **按键2单击**：暂停（原功能）

### 默认参数
当前默认的正弦波参数：
- 振幅：50像素
- 周期：100像素
- 周期数：2个

## 技术实现

### 坐标转换
正弦波的像素坐标通过以下步骤转换为步进电机步数：
1. 计算正弦波坐标：`y = amplitude * sin(2π * x / period)`
2. 像素坐标转角度：`α = atan2(pixel * ImageToActual, Distance)`
3. 角度转步数：`steps = Round(Alpha * α * Step_Sub)`

### 插补算法
使用现有的Bresenham直线插补算法连接各个正弦波坐标点，实现平滑的连续运动。

### 参数说明
- `ImageToActual = 1.93f`: 像素到实际距离转换系数
- `Distance = 1000.0f`: 摄像头到屏幕距离
- `Step_Sub = 207*64`: 步进电机细分参数
- `Alpha = 0.995f`: 坐标转换补偿系数

## 调试信息

系统会通过串口输出详细的调试信息：
- 正弦波参数
- 生成的坐标点
- 转换后的步进电机步数
- 运动状态

## 注意事项

1. **点数限制**：最大支持15个坐标点
2. **参数范围**：建议振幅不超过100像素，周期不小于50像素
3. **安全性**：运动过程中可通过按键中断返回原点
4. **精度**：采用开环控制，精度取决于步进电机和机械结构

## 扩展建议

1. **参数可调**：可通过串口或按键调整正弦波参数
2. **多种波形**：可扩展支持余弦波、三角波等
3. **复合运动**：可实现李萨如图形等复杂轨迹
4. **闭环控制**：结合编码器反馈提高精度

## 编译和使用

1. 确保所有文件都已正确修改
2. 编译项目
3. 下载到STM32F1开发板
4. 通过串口查看调试信息
5. 使用按键控制云台运动
