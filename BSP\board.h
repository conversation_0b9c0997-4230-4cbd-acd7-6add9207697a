#ifndef __BOARD_H
#define __BOARD_H

#include "stm32f10x.h"
#include "stdio.h"
#include "motor.h"

void nvic_init(void);
void clock_init(void);
void usart_init(void);
void board_init(void);
bool Key_Getlevel(void);

void vData_Get(void);// Data conversion
void vSingle_Point_Receive(void);// Receive single point coordinate
void reset_receive_state(void);// Reset receive state

// Camera data related functions
void Camera_ParseData(uint8_t *data);
typedef struct {
    uint16_t center_x;      // Center X coordinate
    uint16_t center_y;      // Center Y coordinate
    int8_t err_x;           // X direction error
    int8_t err_y;           // Y direction error
    uint8_t valid;          // Data valid flag
} CameraData_t;
CameraData_t* Camera_GetData(void);
uint8_t Camera_HasNewData(void);

// LED related functions
void vLED_Init(void);
void vLED_Blink(uint8_t times);

typedef struct _DATAPoint_// Data point structure
{
	int16_t x;// X coordinate
	int16_t y;
	int32_t x_step;
	uint8_t x_max_flag;
	int32_t y_step;
	uint8_t y_max_flag;
	float s;// Speed ratio y speed/x speed
	uint8_t mode;// Mode
	uint8_t num;
}_DATAPoint;

extern int16_t usart_point[1][2];  // Single point array
extern _DATAPoint Usartdata;

#endif
