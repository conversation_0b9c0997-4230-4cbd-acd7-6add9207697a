# 9-AXIS CANFD IMU 通信协议

## CAN 2.0

波特率1M

### ACCEL

数据示例：

```c
ID:0x30  Data:0x77 0x00 0xBA 0xFF 0xDC 0x03 0x00 0x00
```

格式:

```c
uint16_t x;
uint16_t y;
uint16_t z;
uint16_t res;
```

解析方式

```c
float raw_data = float(x) / 32767.0f * 32.0f;
```

### GYRO

数据示例：

```c
ID:0x31  Data:0xF6 0xFF 0xE4 0xFF 0xFC 0xFF 0x00 0x00
```

格式:

```c
uint16_t x;
uint16_t y;
uint16_t z;
uint16_t res;
```

解析方式

```c
float raw_data = float(x) / 32767.0f * 34.90658502f * 2.0f;
```

### MAGN

数据示例：

```c
ID:0x32  Data:0x00 0xC1 0xE2 0x65 0x92 0x21 0x00 0x00
```

格式:

```c
uint16_t x;
uint16_t y;
uint16_t z;
uint16_t res;
```

解析方式

```c
float raw_data = float(x) / 32767.0f * 4.0f;
```

### EULR

数据示例：

```c
ID:0x33  Data:0xB8 0x00 0x6B 0x00 0x0F 0x1A 0x00 0x00
```

格式:

```c
uint16_t pit;
uint16_t rol;
uint16_t yaw;
uint16_t res;
```

解析方式

```c
float raw_data = float(pit) / 32767.0f * M_2PI;
```

### QUAT

数据示例：

```c
ID:0x34  Data:0x57 0x33 0x84 0x00 0x32 0x01 0x2E 0x26
```

格式:

```c
uint16_t q0;
uint16_t q1;
uint16_t q2;
uint16_t q3;
```

解析方式

```c
float raw_data = float(q0) / 32767.0f * 2.0f;
```

## CANFD

波特率5M

数据格式：

```c
struct __attribute__((packed)) {
      struct {
        float q0;
        float q1;
        float q2;
        float q3;
      } quat;
      struct {
        float x;
        float y;
        float z;
      } gyro;
      struct {
        float x;
        float y;
        float z;
      } accel;
      struct {
        float x;
        float y;
        float z;
      } magn;
      struct __attribute__((packed)) {
        float yaw;
        float pit;
        float rol;
      } eulr_;
    }
```

示例数据：

```c
ID: 0x30
Data: 0xAB 0x13 0x30 0x3F 0xC1 0x23 0x9E 0x3E 0x42 0xDB 0xC8 0xBE 0xA0 0xE3 0x6 0x3F 0x56 0x6A 0xE5 0xBA 0xD0 0x9F 0x6 0xBC 0xA8 0xBD 0x81 0x3A 0x0 0xC0 0x5 0x3F 0x0 0x0 0xC4 0xBD 0x0 0xE0 0x5C 0xBF 0x3A 0xB4 0x48 0x3D 0xCD 0xCC 0xCC 0xBC 0x92 0xED 0xBC 0x3E 0x2B 0xAA 0xA5 0x3F 0x41 0x48 0xBB 0x3C 0xC6 0x9B 0xA7 0x40
```

## 串口数据

连接到DataUSB口，波特率1M

```c
public:
typedef struct __attribute__((packed)) {
    uint8_t prefix;
    uint8_t id;
    struct __attribute__((packed)) {
        struct {
            float q0;
            float q1;
            float q2;
            float q3;
        } quat;

        struct {
            float x;
            float y;
            float z;
        } gyro;

        struct {
            float x;
            float y;
            float z;
        } accel;

        struct {
            float x;
            float y;
            float z;
        } magn;

        struct __attribute__((packed)) {
            float yaw;
            float pit;
            float rol;
        } eulr_;

    } raw;
    uint8_t crc8;
};
```

示例数据：

```c
0xA5 0x30 0x0B 0x71 0x4C 0x3F 0xC3 0x65 0x53 0x3D 0x66 0x68 0xD7 0xBC 0x90 0x5C 0x19 0x3F 0xA2 0x4D 0x28 0xBC 0x10 0x58 0x6F 0xBC 0xE6 0x20 0x64 0xBC 0x00 0xC0 0x5B 0x3F 0x00 0xC0 0x87 0x3E 0x00 0x00 0xC9 0xBE 0x29 0x5C 0x0F 0x3E 0x34 0x33 0x33 0x3E 0x8C 0x6C 0xA7 0x3E 0x52 0x6B 0xA4 0x3F 0x50 0xD3 0x51 0x3D 0xB4 0xBB 0xC5 0x40 0x70
```

## 命令行

连接到Debug USB口，波特率460800。磁力计和陀螺仪出厂前已经校准完成。

### 校准陀螺仪

1. 连接5v电源后，等待IMU温度恒定。可通过`icm42688 show 1 1`命令查看温度。
2. 将IMU静置到桌子上，在命令行中输入`icm42688 cali`，等待校准完成，校准过程中不要敲击键盘或进行其他动作，大概耗时一分钟。

### 校准磁力计

将IMU远离电脑、墙壁、桌子等金属物品，在命令行中输入`mmc5603 cali`，等待校准完成中手动将陀螺仪在空中旋转到每个可能的角度，大概耗时一分钟。

如何判断有无磁场干扰：在命令行输入`mmc5603 show 1 1`，查看输出的`intensity`字段，如果在1.0附近证明无磁场干扰。

有磁场干扰示例：`x:-0.111000 y:-0.019000 z:+0.538000 intensity:1.203785`

无磁场干扰示例：`x:-0.106000 y:-0.420000 z:+0.001000 intensity:1.001009`


### 其他

用法：`set_imu enable/disable     [accl/gyro/magn/raw_magn/quat/eulr/canfd]`

示例：

* 开启陀螺仪输出： `set_imu enable gyro`
* 开启CANFD输出: `set_imu enable canfd`

PS: 由于陀螺仪非常容易受到干扰，影响解算精度。当检测到陀螺仪受到干扰时会自动切换到六轴模式，此时磁力计输出的xyz轴数据全部为0。通过`set_imu enable raw_magn`可以输出受干扰的磁力计数据原始数据。
