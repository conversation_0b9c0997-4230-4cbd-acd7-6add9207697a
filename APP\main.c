#include "board.h"
#include "stdio.h"

/*
* 简化版主函数：
* 只保留必要的初始化和串口3接收解析功能
* 持续接收并解析串口3数据，无需按键控制
*/

int main(void)
{
	// 系统初始化
	board_init();

	// 初始化步进计数器
	Usartdata.x_step = 0;
	Usartdata.y_step = 0;

	printf("System ready, USART3 receiving started...\r\n");
	vLED_Blink(2);  // LED闪烁2次表示系统就绪

	// 主循环：持续接收和解析串口3数据
	static uint32_t heartbeat_counter = 0;

	for(;;)
	{
		// 检查是否有新的串口3数据并解析
		vData_Get();  // 这个函数会自动检查、解析并打印接收到的数据

		// 心跳打印，每100000次循环打印一次
		heartbeat_counter++;
		if(heartbeat_counter >= 100000)
		{
			printf("Heartbeat: waiting for USART3 data...\r\n");
			heartbeat_counter = 0;
		}
	}
}
