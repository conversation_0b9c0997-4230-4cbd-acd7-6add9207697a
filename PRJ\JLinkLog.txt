T55F4 025:104 SEGGER J-Link V6.30h Log File (0000ms, 0463ms total)
T55F4 025:104 DLL Compiled: Mar 16 2018 18:02:51 (0000ms, 0463ms total)
T55F4 025:104 Logging started @ 2019-09-28 16:25 (0000ms, 0463ms total)
T55F4 025:104 JLINK_SetWarnOutHandler(...) (0000ms, 0463ms total)
T55F4 025:104 JLINK_OpenEx(...)
Firmware: J-Link V9 compiled Apr 20 2018 16:47:26
Hardware: V9.30
S/N: 59302535
Feature(s): FlashBP, GDB, RDI, FlashDL, JFlash, GDBFull
TELNET listener socket opened on port 19021WEBSRV 
Starting webserver (0084ms, 0547ms total)
T55F4 025:104 WEBSRV Webserver running on local port 19080 (0084ms, 0547ms total)
T55F4 025:104   returns O.K. (0084ms, 0547ms total)
T55F4 025:188 JLINK_GetEmuCaps()  returns 0xB9FF7BBF (0000ms, 0547ms total)
T55F4 025:189 JLINK_TIF_GetAvailable(...) (0000ms, 0547ms total)
T55F4 025:189 JLINK_SetErrorOutHandler(...) (0000ms, 0547ms total)
T55F4 025:189 JLINK_ExecCommand("ProjectFile = "F:\Git\STM32\Template\PRJ\JLinkSettings.ini"", ...).   returns 0x00 (0001ms, 0548ms total)
T55F4 025:190 JLINK_ExecCommand("Device = STM32F103C8", ...). Device "STM32F103C8" selected.  returns 0x00 (0001ms, 0549ms total)
T55F4 025:191 JLINK_ExecCommand("DisableConnectionTimeout", ...).   returns 0x01 (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetHardwareVersion()  returns 0x16B48 (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetDLLVersion()  returns 63008 (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetFirmwareString(...) (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetDLLVersion()  returns 63008 (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetCompileDateTime() (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetFirmwareString(...) (0000ms, 0549ms total)
T55F4 025:191 JLINK_GetHardwareVersion()  returns 0x16B48 (0000ms, 0549ms total)
T55F4 025:191 JLINK_TIF_Select(JLINKARM_TIF_SWD)  returns 0x00 (0000ms, 0549ms total)
T55F4 025:191 JLINK_SetSpeed(5000) (0001ms, 0550ms total)
T55F4 025:192 JLINK_GetId() >0x10B TIF>Found SW-DP with ID 0x1BA01477 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF>
 >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x10B TIF>Found SW-DP with ID 0x1BA01477 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF>Scanning AP map to find all available APs >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF>
 >0x0D TIF> >0x21 TIF>AP[1]: Stopped AP scan as end of AP map has been reachedAP[0]: AHB-AP (IDR: 0x14770011)Iterating through AP map to find AHB-AP to use >0x42 TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x42 TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF>AP[0]: Core foundAP[0]: AHB-AP ROM base: 0xE00FF000 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF>
 >0x21 TIF>CPUID register: 0x411FC231. Implementer code: 0x41 (ARM)Found Cortex-M3 r1p1, Little endian. -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0x********)FPUnit: 6 code (BP) slots and 2 literal slots -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_ReadMem(4 bytes @ 0xE000ED88) -- CPU_WriteMem(4 bytes @ 0xE000ED88) -- CPU_ReadMem(4 bytes @ 0xE000ED88) -- CPU_WriteMem(4 bytes @ 0xE000ED88)CoreSight components:
ROMTbl[0] @ E00FF000 -- CPU_ReadMem(16 bytes @ 0xE00FF000) -- CPU_ReadMem(16 bytes @ 0xE000EFF0) -- CPU_ReadMem(16 bytes @ 0xE000EFE0)ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 001BB000 SCS -- CPU_ReadMem(16 bytes @ 0xE0001FF0) -- CPU_ReadMem(16 bytes @ 0xE0001FE0)ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 001BB002 DWT -- CPU_ReadMem(16 bytes @ 0xE0002FF0) -- CPU_ReadMem(16 bytes @ 0xE0002FE0)ROMTbl[0][2]: ********, CID: B105E00D, PID: 000BB003 FPB -- CPU_ReadMem(16 bytes @ 0xE0000FF0)
 -- CPU_ReadMem(16 bytes @ 0xE0000FE0)ROMTbl[0][3]: ********, CID: B105E00D, PID: 001BB001 ITM -- CPU_ReadMem(16 bytes @ 0xE00FF010) -- CPU_ReadMem(16 bytes @ 0xE0040FF0) -- CPU_ReadMem(16 bytes @ 0xE0040FE0)ROMTbl[0][4]: ********, CID: B105900D, PID: 001BB923 TPIU-Lite >0x0D TIF> >0x21 TIF>  returns 0x1BA01477 (0125ms, 0675ms total)
T55F4 025:317 JLINK_GetDLLVersion()  returns 63008 (0000ms, 0675ms total)
T55F4 025:317 JLINK_CORE_GetFound()  returns 0x30000FF (0000ms, 0675ms total)
T55F4 025:317 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX) -- Value=0xE00FF000  returns 0x00 (0000ms, 0675ms total)
T55F4 025:317 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX) -- Value=0xE00FF000  returns 0x00 (0000ms, 0675ms total)
T55F4 025:317 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0675ms total)
T55F4 025:317 JLINK_ReadMemEx(0xE0041FF0, 0x0010 Bytes, ..., Flags = 0x02000004) -- CPU_ReadMem(16 bytes @ 0xE0041FF0) - Data: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  returns 0x10 (0001ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX) -- Value=0x********  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX) -- Value=0x********  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX) -- Value=0xE0001000  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX) -- Value=0x********  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX) -- Value=0xE000E000  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX) -- Value=0xE000EDF0  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x01 = Unknown) -- Value=0x00000000  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_ReadMemU32(0xE000ED00, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED00) - Data: 31 C2 1F 41  returns 0x01 (0000ms, 0676ms total)
T55F4 025:318 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0676ms total)
T55F4 025:318 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)  returns JLINKARM_CM3_RESET_TYPE_NORMAL (0000ms, 0676ms total)
T55F4 025:318 JLINK_Reset() -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDFC)Reset: Halt core after reset via DEMCR.VC_CORERESET. >0x35 TIF>Reset: Reset device via AIRCR.SYSRESETREQ. -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000ED0C) >0x0D TIF> >0x28 TIF> -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDFC)
 -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0x********) -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) (0063ms, 0739ms total)
T55F4 025:381 JLINK_ReadReg(R15 (PC))  returns 0x08000144 (0000ms, 0739ms total)
T55F4 025:381 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 0739ms total)
T55F4 025:381 JLINK_Halt()  returns 0x00 (0000ms, 0739ms total)
T55F4 025:381 JLINK_ReadMemU32(0xE000EDF0, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) - Data: 03 00 03 00  returns 0x01 (0000ms, 0739ms total)
T55F4 025:381 JLINK_WriteU32(0xE000EDF0, 0xA05F0003) -- CPU_WriteMem(4 bytes @ 0xE000EDF0)  returns 0x00 (0000ms, 0739ms total)
T55F4 025:381 JLINK_WriteU32(0xE000EDFC, 0x01000000) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)  returns 0x00 (0001ms, 0740ms total)
T55F4 025:382 JLINK_GetHWStatus(...)  returns 0x00 (0000ms, 0740ms total)
T55F4 025:382 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)  returns 0x06 (0000ms, 0740ms total)
T55F4 025:382 JLINK_GetNumBPUnits(Type = 0xF0)  returns 0x2000 (0000ms, 0740ms total)
T55F4 025:382 JLINK_GetNumWPUnits()  returns 0x04 (0000ms, 0740ms total)
T55F4 025:382 JLINK_GetSpeed()  returns 0xFA0 (0000ms, 0740ms total)
T55F4 025:382 JLINK_ReadMemU32(0xE000E004, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000E004) - Data: 01 00 00 00  returns 0x01 (0000ms, 0740ms total)
T55F4 025:382 JLINK_ReadMemU32(0xE000E004, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000E004) - Data: 01 00 00 00  returns 0x01 (0000ms, 0740ms total)
T55F4 025:382 JLINK_WriteMemEx(0xE0001000, 0x001C Bytes, ..., Flags = 0x02000004) - Data: 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ... -- CPU_WriteMem(28 bytes @ 0xE0001000)  returns 0x1C (0001ms, 0741ms total)
T55F4 025:383 JLINK_ReadReg(R15 (PC))  returns 0x08000144 (0000ms, 0741ms total)
T55F4 025:383 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 0741ms total)
T55F4 025:386 JLINK_SWO_Control(JLINKARM_SWO_CMD_GET_SPEED_INFO, ...)  returns 0x00 (0000ms, 0741ms total)
T55F4 025:386 JLINK_SWO_Control(JLINKARM_SWO_CMD_STOP, ...)  returns 0x00 (0000ms, 0741ms total)
T55F4 025:386 JLINK_SWO_Control(JLINKARM_SWO_CMD_START, ...) -- UART -- 6545454bps -- CPU_ReadMem(4 bytes @ 0xE0042004)  returns 0x00 (0001ms, 0742ms total)
T55F4 025:387 JLINK_WriteU32(0xE0040004, 0x00000001) -- CPU_WriteMem(4 bytes @ 0xE0040004)  returns 0x00 (0000ms, 0742ms total)
T55F4 025:387 JLINK_WriteU32(0xE0040010, 0x0000000A)  returns 0x00 (0000ms, 0742ms total)
T55F4 025:387 JLINK_WriteU32(0xE00400F0, 0x00000002)  returns 0x00 (0000ms, 0742ms total)
T55F4 025:387 JLINK_WriteU32(0xE0040304, 0x00000000)  returns 0x00 (0000ms, 0742ms total)
T55F4 025:387 JLINK_WriteU32(0xE0000FB0, 0xC5ACCE55) -- CPU_WriteMem(4 bytes @ 0xE0000FB0)  returns 0x00 (0001ms, 0743ms total)
T55F4 025:388 JLINK_WriteU32(0xE0000E80, 0x0001000D)  returns 0x00 (0000ms, 0743ms total)
T55F4 025:388 JLINK_WriteU32(0xE0000E00, 0x00000001)  returns 0x00 (0000ms, 0743ms total)
T55F4 025:388 JLINK_WriteU32(0xE0000E40, 0x00000000)  returns 0x00 (0000ms, 0743ms total)
T55F4 025:388 JLINK_WriteU32(0xE0001000, 0x00010A1F)  returns 0x00 (0000ms, 0743ms total)
T55F4 025:463 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)  returns JLINKARM_CM3_RESET_TYPE_NORMAL (0000ms, 0743ms total)
T55F4 025:463 JLINK_Reset() -- CPU_WriteMem(4 bytes @ 0xE0000E00) -- CPU_WriteMem(4 bytes @ 0xE0000E40) -- CPU_WriteMem(4 bytes @ 0xE0000E80) -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0040010) -- CPU_WriteMem(4 bytes @ 0xE00400F0) -- CPU_WriteMem(4 bytes @ 0xE0040304) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)Reset: Halt core after reset via DEMCR.VC_CORERESET. >0x35 TIF>Reset: Reset device via AIRCR.SYSRESETREQ.
 -- CPU_WriteMem(4 bytes @ 0xE000ED0C) >0x0D TIF> >0x28 TIF> -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0x********) -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_ReadMem(4 bytes @ 0xE0042004) (0064ms, 0807ms total)
T55F4 025:528 JLINK_WriteU32(0xE000EDFC, 0x01000000) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)  returns 0x00 (0000ms, 0807ms total)
T55F4 025:528 JLINK_ReadReg(R15 (PC))  returns 0x08000144 (0000ms, 0807ms total)
T55F4 025:528 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 0807ms total)
T55F4 025:635 JLINK_ReadMemEx(0x08000144, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(64 bytes @ 0x08000140) -- Updating C cache (64 bytes @ 0x08000140) -- Read from C cache (60 bytes @ 0x08000144) - Data: 06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...  returns 0x3C (0000ms, 0807ms total)
T55F4 025:697 JLINK_WriteU32(0xE0001008, 0x00000000)  returns 0x00 (0000ms, 0807ms total)
T55F4 025:697 JLINK_WriteU32(0xE000100C, 0x00000000)  returns 0x00 (0000ms, 0807ms total)
T55F4 025:697 JLINK_WriteU32(0xE0001010, 0x00000000)  returns 0x00 (0000ms, 0807ms total)
T55F4 025:697 JLINK_WriteU32(0xE0001014, 0x00000000)  returns 0x00 (0000ms, 0807ms total)
T55F4 025:697 JLINK_WriteU32(0xE0001018, 0x00000000)  returns 0x00 (0000ms, 0807ms total)
T10224 026:501 JLINK_ReadMemEx(0x08000144, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from C cache (2 bytes @ 0x08000144) - Data: 06 48  returns 0x02 (0000ms, 0807ms total)
T10224 026:501 JLINK_SetBPEx(Addr = 0x08000364, Type = 0xFFFFFFF2)  returns 0x00000001 (0000ms, 0807ms total)
T10224 026:501 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) - Data: 00 00 00 00  returns 0x01 (0000ms, 0807ms total)
T10224 026:501 JLINK_WriteU32(0xE0001004, 0x000003FF)  returns 0x00 (0000ms, 0807ms total)
T10224 026:501 JLINK_WriteU32(0xE0001000, 0x00410A1F)  returns 0x00 (0000ms, 0807ms total)
T10224 026:501 JLINK_WriteU32(0xE0000E80, 0x0001000F)  returns 0x00 (0000ms, 0807ms total)
T10224 026:501 JLINK_SWO_Control(JLINKARM_SWO_CMD_GET_NUM_BYTES, ...)  returns 0x00 (0000ms, 0807ms total)
T10224 026:501 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0x********) -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0000E80) -- CPU_WriteMem(4 bytes @ 0xE0001008) -- CPU_WriteMem(4 bytes @ 0xE000100C) -- CPU_WriteMem(4 bytes @ 0xE0001010) -- CPU_WriteMem(4 bytes @ 0xE0001014) -- CPU_WriteMem(4 bytes @ 0xE0001018) -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010)
 -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) -- CPU_WriteMem(4 bytes @ 0xE0001004) (0004ms, 0811ms total)
T10224 026:505 JLINK_WriteU32(0xE0001000, 0x00010A1F) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE0001000)  returns 0x00 (0000ms, 0811ms total)
T10224 026:607 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: 00 00 00 00 00 00 00 00 00 C0 FF 88 7A C0 FF 88 ...  NumBytesRead = 0x15 (0000ms, 0811ms total)
T10224 026:609 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0811ms total)
T10224 026:609 JLINK_IsHalted()  returns TRUE (0001ms, 0812ms total)
T10224 026:611 JLINK_Halt()  returns 0x00 (0000ms, 0811ms total)
T10224 026:611 JLINK_IsHalted()  returns TRUE (0000ms, 0811ms total)
T10224 026:611 JLINK_IsHalted()  returns TRUE (0000ms, 0811ms total)
T10224 026:611 JLINK_IsHalted()  returns TRUE (0000ms, 0811ms total)
T10224 026:611 JLINK_ReadReg(R15 (PC))  returns 0x08000364 (0000ms, 0811ms total)
T10224 026:611 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 0811ms total)
T10224 026:611 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0811ms total)
T10224 026:613 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0811ms total)
T10224 026:613 JLINK_ClrBPEx(BPHandle = 0x00000001)  returns 0x00 (0000ms, 0811ms total)
T10224 026:613 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) - Data: 02 00 00 00  returns 0x01 (0000ms, 0811ms total)
T10224 026:613 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) - Data: 00 00 00 00  returns 0x01 (0001ms, 0812ms total)
T10224 026:614 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) - Data: 00 02 00 00  returns 0x01 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) - Data: 00 00 00 00  returns 0x01 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) - Data: 00 00 00 00  returns 0x01 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R0)  returns 0x08000365 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R1)  returns 0x20000408 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R2)  returns 0x00000000 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R3)  returns 0x08000311 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R4)  returns 0x080003A0 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R5)  returns 0x080003A0 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 0812ms total)
T10224 026:614 JLINK_ReadReg(R8)  returns 0x00000000 (0001ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R13 (SP))  returns 0x20000408 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R14)  returns 0x08000185 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(R15 (PC))  returns 0x08000364 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(MSP)  returns 0x20000408 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(PSP)  returns 0x20001000 (0000ms, 0813ms total)
T10224 026:615 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 0813ms total)
T55F4 026:620 JLINK_ReadMemEx(0x08000264, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(128 bytes @ 0x08000240) -- Updating C cache (128 bytes @ 0x08000240) -- Read from C cache (60 bytes @ 0x08000264) - Data: 08 46 40 68 40 F0 02 00 48 60 00 BF 03 48 40 68 ...  returns 0x3C (0001ms, 0814ms total)
T55F4 026:621 JLINK_ReadMemEx(0x080002A0, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(64 bytes @ 0x080002C0) -- Updating C cache (64 bytes @ 0x080002C0) -- Read from C cache (60 bytes @ 0x080002A0) - Data: 08 40 0E 49 48 60 08 46 00 68 0E 49 08 40 0B 49 ...  returns 0x3C (0000ms, 0814ms total)
T55F4 026:621 JLINK_ReadMemEx(0x080002EC, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(64 bytes @ 0x08000300) -- Updating C cache (64 bytes @ 0x08000300) -- Read from C cache (60 bytes @ 0x080002EC) - Data: 00 BF FE E7 01 4A 02 49 00 F0 13 B8 41 03 00 08 ...  returns 0x3C (0001ms, 0815ms total)
T55F4 026:622 JLINK_ReadMemEx(0x08000328, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(64 bytes @ 0x08000340) -- Updating C cache (64 bytes @ 0x08000340) -- Read from C cache (60 bytes @ 0x08000328) - Data: 00 25 03 E0 39 46 B0 47 64 1C 6D 1C 20 78 00 28 ...  returns 0x3C (0000ms, 0815ms total)
T55F4 026:622 JLINK_ReadMemEx(0x08000364, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(64 bytes @ 0x08000380) -- Updating C cache (64 bytes @ 0x08000380) -- Read from C cache (60 bytes @ 0x08000364) - Data: 02 A0 FF F7 C3 FF 00 BF FE E7 00 00 75 73 61 72 ...  returns 0x3C (0001ms, 0816ms total)
T10224 028:182 JLINK_ReadMemEx(0x08000364, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from C cache (2 bytes @ 0x08000364) - Data: 02 A0  returns 0x02 (0000ms, 0816ms total)
T10224 028:182 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001004) - Data: 4D 1B 00 00  returns 0x01 (0000ms, 0816ms total)
T10224 028:182 JLINK_WriteU32(0xE0001004, 0x000003FF)  returns 0x00 (0000ms, 0816ms total)
T10224 028:182 JLINK_WriteU32(0xE0001000, 0x00410A1F)  returns 0x00 (0000ms, 0816ms total)
T10224 028:182 JLINK_WriteU32(0xE0000E80, 0x0001000F)  returns 0x00 (0000ms, 0816ms total)
T10224 028:182 JLINK_SWO_Control(JLINKARM_SWO_CMD_GET_NUM_BYTES, ...)  returns 0xE4 (0000ms, 0816ms total)
T10224 028:182 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0816ms total)
T10224 028:182 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0000E80) -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE0001004) (0002ms, 0818ms total)
T10224 028:184 JLINK_WriteU32(0xE0001000, 0x00010A1F) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE0001000)  returns 0x00 (0000ms, 0818ms total)
T10224 028:286 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: 05 20 C0 86 E1 33 01 75 C0 49 01 73 C0 2F 01 61 ...  NumBytesRead = 0x3D (0000ms, 0818ms total)
T10224 028:288 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:288 JLINK_IsHalted()  returns FALSE (0001ms, 0819ms total)
T10224 028:390 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 028:392 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:392 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 028:492 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 028:494 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:494 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 028:595 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 028:597 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:597 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 028:698 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 028:700 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:700 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001004) - Data: BC EB 36 02  returns 0x01 (0000ms, 0818ms total)
T10224 028:703 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 028:803 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 028:805 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:805 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 028:906 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 028:908 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 028:908 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:010 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:012 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:012 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:113 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:115 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:115 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:216 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: 00 00 00 00 00 80 C0 FF 88 7A C0 FF 88 7A C0 FF ...  NumBytesRead = 0x16 (0000ms, 0818ms total)
T10224 029:218 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:218 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) - Data: BC EB 36 02  returns 0x01 (0000ms, 0818ms total)
T10224 029:220 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:321 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 029:323 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:323 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:424 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:426 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:426 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:528 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:530 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:530 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:630 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:632 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:632 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:733 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 029:735 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:735 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) - Data: BC EB 36 02  returns 0x01 (0000ms, 0818ms total)
T10224 029:737 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:838 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:840 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:840 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 029:941 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 029:943 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 029:943 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:045 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 030:047 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:047 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:148 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A 00 00 00 00 00 80 C0 FF 88 7A C0 FF ...  NumBytesRead = 0x16 (0000ms, 0818ms total)
T10224 030:150 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:150 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:252 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 030:254 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:254 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) - Data: BC EB 36 02  returns 0x01 (0000ms, 0818ms total)
T10224 030:256 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:358 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 030:360 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:360 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:461 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 030:463 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:463 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:564 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 030:566 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:566 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:666 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 030:668 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:668 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:769 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 030:771 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:771 JLINK_ReadMemU32(0xE0001004, 0x0001 Items, ...) - Data: BC EB 36 02  returns 0x01 (0000ms, 0818ms total)
T10224 030:773 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:874 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x0C (0000ms, 0818ms total)
T10224 030:876 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:876 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 030:976 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A C0 FF 88 7A  NumBytesRead = 0x10 (0000ms, 0818ms total)
T10224 030:978 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0818ms total)
T10224 030:978 JLINK_IsHalted()  returns FALSE (0000ms, 0818ms total)
T10224 031:079 JLINK_Halt()  returns 0x00 (0002ms, 0820ms total)
T10224 031:081 JLINK_IsHalted()  returns TRUE (0000ms, 0820ms total)
T10224 031:081 JLINK_IsHalted()  returns TRUE (0000ms, 0820ms total)
T10224 031:081 JLINK_IsHalted()  returns TRUE (0000ms, 0820ms total)
T10224 031:081 JLINK_ReadReg(R15 (PC))  returns 0x0800036C (0000ms, 0820ms total)
T10224 031:081 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 0820ms total)
T10224 031:081 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data: 00 00 00 00 00 80 C0 FF 88 7A C0 FF 88 7A C0 FF ...  NumBytesRead = 0x16 (0000ms, 0820ms total)
T10224 031:083 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0820ms total)
T10224 031:083 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) - Data: 01 00 00 00  returns 0x01 (0000ms, 0820ms total)
T10224 031:083 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) - Data: 00 00 00 00  returns 0x01 (0001ms, 0821ms total)
T10224 031:084 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) - Data: 00 02 00 00  returns 0x01 (0000ms, 0821ms total)
T10224 031:084 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) - Data: 00 00 00 00  returns 0x01 (0000ms, 0821ms total)
T10224 031:084 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) - Data: 00 00 00 00  returns 0x01 (0001ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R0)  returns 0x0000000D (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R1)  returns 0x20000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R2)  returns 0x00000001 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R3)  returns 0x******** (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R4)  returns 0x080003A0 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R5)  returns 0x080003A0 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R13 (SP))  returns 0x20000408 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R14)  returns 0x08000331 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(R15 (PC))  returns 0x0800036C (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(MSP)  returns 0x20000408 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(PSP)  returns 0x20001000 (0000ms, 0822ms total)
T10224 031:085 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 0822ms total)
T55F4 031:091 JLINK_ReadMemEx(0x0800036C, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(128 bytes @ 0x08000340) -- Updating C cache (128 bytes @ 0x08000340) -- Read from C cache (60 bytes @ 0x0800036C) - Data: FE E7 00 00 75 73 61 72 74 20 64 6F 6E 65 21 0A ...  returns 0x3C (0000ms, 0822ms total)
T55F4 032:505 JLINK_Close() -- CPU_ReadMem(4 bytes @ 0xE0001000) (0012ms, 0834ms total)
T55F4 032:505  (0012ms, 0834ms total)
T55F4 032:505 Closed (0012ms, 0834ms total)
