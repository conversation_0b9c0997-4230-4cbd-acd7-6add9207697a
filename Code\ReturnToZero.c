#include "ReturnToZero.h"

uint8_t rxCmd[128] = {0}; uint8_t rxCount = 0;

// 临时重定向printf来屏蔽Emm_V5库的乱码输出
int original_fputc_enabled = 1;  // 改为全局变量，去掉static

// 临时禁用printf输出
void disable_printf_output(void) {
    original_fputc_enabled = 0;
}

// 恢复printf输出
void enable_printf_output(void) {
    original_fputc_enabled = 1;
}

void vMotor_Enable(void)
{
	disable_printf_output();  // 禁用printf输出
	Emm_V5_En_Control(M0_addr,true,true);
	delay_ms(10);
	Emm_V5_En_Control(M1_addr,true,true);
	delay_ms(10);
	Emm_V5_Synchronous_motion(All_addr);
	delay_ms(10);
	enable_printf_output();   // 恢复printf输出
}

void vMotor_Disenable(void)
{
	disable_printf_output();  // 禁用printf输出
	Emm_V5_En_Control(M0_addr,false,true);
	delay_ms(10);
	Emm_V5_En_Control(M1_addr,false,true);
	delay_ms(10);
	Emm_V5_Synchronous_motion(All_addr);
	delay_ms(10);
	enable_printf_output();   // 恢复printf输出
	delay_ms(10);
}

void vWait_Zero(void)
{
	for(;;)
	{
		if(Key_Getlevel()==true)
		{
			disable_printf_output();  // 禁用printf输出
			Emm_V5_Reset_CurPos_To_Zero(M0_addr);
			delay_ms(10);
			Emm_V5_Reset_CurPos_To_Zero(M1_addr);
			delay_ms(10);
			enable_printf_output();   // 恢复printf输出
			break;
		}
	}
}

void vReturn_To_Zero(void)
{
	// Parameter conversion description:
	// ZDT_X42_V2_Traj_Position_Control(addr, dir, acc, dec, velocity, position, raf, snF)
	// Emm_V5_Pos_Control(addr, dir, vel, acc, clk, raF, snF)
	// Original params: M0_addr,0,2000,2500,800,0.0f,1,1
	// Converted to: M0_addr,0,800,200,0,true,true (speed 800, acc 200, pulse 0, abs pos, sync)
	Emm_V5_Pos_Control(M0_addr, 0, 800, 200, 0, true, true);
	delay_ms(10);
	Emm_V5_Pos_Control(M1_addr, 0, 800, 200, 0, true, true);
	delay_ms(10);
	Emm_V5_Synchronous_motion(All_addr);
	delay_ms(10);
}
