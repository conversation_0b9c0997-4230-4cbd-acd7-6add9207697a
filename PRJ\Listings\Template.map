Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to board.o(i.board_init) for board_init
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to board.o(i.vLED_Blink) for vLED_Blink
    main.o(i.main) refers to board.o(i.vData_Get) for vData_Get
    main.o(i.main) refers to board.o(.bss) for Usartdata
    main.o(i.main) refers to main.o(.data) for heartbeat_counter
    motor.o(i.Motor_Timer_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    motor.o(i.PID_Control) refers to fflti.o(.text) for __aeabi_i2f
    motor.o(i.PID_Control) refers to fadd.o(.text) for __aeabi_fsub
    motor.o(i.PID_Control) refers to fmul.o(.text) for __aeabi_fmul
    motor.o(i.PID_Control) refers to ffixi.o(.text) for __aeabi_f2iz
    motor.o(i.Pwm_UIE_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    motor.o(i.TIM3_IRQHandler) refers to board.o(.bss) for Usartdata
    motor.o(i.TIM4_IRQHandler) refers to board.o(.bss) for Usartdata
    motor.o(i.vDraw_Sine_Wave) refers to printfa.o(i.__0printf) for __2printf
    motor.o(i.vDraw_Sine_Wave) refers to motor.o(i.vGenerate_Sine_Wave) for vGenerate_Sine_Wave
    motor.o(i.vDraw_Sine_Wave) refers to motor.o(i.vInterpolation_Move) for vInterpolation_Move
    motor.o(i.vDriverInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.vDriverInit) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.vDriverInit) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.vDriverInit) refers to printfa.o(i.__0printf) for __2printf
    motor.o(i.vDriverInit) refers to board.o(.bss) for Usartdata
    motor.o(i.vGenerate_Sine_Wave) refers to ffltui.o(.text) for __aeabi_ui2f
    motor.o(i.vGenerate_Sine_Wave) refers to fflti.o(.text) for __aeabi_i2f
    motor.o(i.vGenerate_Sine_Wave) refers to fdiv.o(.text) for __aeabi_fdiv
    motor.o(i.vGenerate_Sine_Wave) refers to printfa.o(i.__0printf) for __2printf
    motor.o(i.vGenerate_Sine_Wave) refers to fmul.o(.text) for __aeabi_fmul
    motor.o(i.vGenerate_Sine_Wave) refers to fadd.o(.text) for __aeabi_fadd
    motor.o(i.vGenerate_Sine_Wave) refers to f2d.o(.text) for __aeabi_f2d
    motor.o(i.vGenerate_Sine_Wave) refers to sin.o(i.sin) for sin
    motor.o(i.vGenerate_Sine_Wave) refers to dflti.o(.text) for __aeabi_i2d
    motor.o(i.vGenerate_Sine_Wave) refers to dmul.o(.text) for __aeabi_dmul
    motor.o(i.vGenerate_Sine_Wave) refers to d2f.o(.text) for __aeabi_d2f
    motor.o(i.vGenerate_Sine_Wave) refers to atan2.o(i.atan2) for atan2
    motor.o(i.vGenerate_Sine_Wave) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    motor.o(i.vGenerate_Sine_Wave) refers to ffixi.o(.text) for __aeabi_f2iz
    motor.o(i.vGenerate_Sine_Wave) refers to motor.o(.bss) for actual_point
    motor.o(i.vImage_To_Actual) refers to fflti.o(.text) for __aeabi_i2f
    motor.o(i.vImage_To_Actual) refers to fmul.o(.text) for __aeabi_fmul
    motor.o(i.vImage_To_Actual) refers to f2d.o(.text) for __aeabi_f2d
    motor.o(i.vImage_To_Actual) refers to atan2.o(i.atan2) for atan2
    motor.o(i.vImage_To_Actual) refers to d2f.o(.text) for __aeabi_d2f
    motor.o(i.vImage_To_Actual) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    motor.o(i.vImage_To_Actual) refers to fadd.o(.text) for __aeabi_fadd
    motor.o(i.vImage_To_Actual) refers to ffixi.o(.text) for __aeabi_f2iz
    motor.o(i.vImage_To_Actual) refers to motor.o(i.vInterpolation_Move) for vInterpolation_Move
    motor.o(i.vImage_To_Actual) refers to motor.o(.bss) for actual_point
    motor.o(i.vInterpolation_Move) refers to motor.o(i.vInterpolation_Point) for vInterpolation_Point
    motor.o(i.vInterpolation_Move) refers to motor.o(.bss) for actual_point
    motor.o(i.vInterpolation_Move) refers to motor.o(.data) for ReturnToZero_Flag
    motor.o(i.vInterpolation_Point) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.vInterpolation_Point) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.vInterpolation_Point) refers to motor.o(i.vOne_Step_Y) for vOne_Step_Y
    motor.o(i.vInterpolation_Point) refers to motor.o(i.vOne_Step_X) for vOne_Step_X
    motor.o(i.vInterpolation_Point) refers to board.o(i.Key_Getlevel) for Key_Getlevel
    motor.o(i.vInterpolation_Point) refers to motor.o(.data) for now_x
    motor.o(i.vInterpolation_Point) refers to board.o(.bss) for Usartdata
    motor.o(i.vOne_Step_X) refers to motor.o(i.Delayms) for Delayms
    motor.o(i.vOne_Step_X) refers to board.o(.bss) for Usartdata
    motor.o(i.vOne_Step_Y) refers to motor.o(i.Delayms) for Delayms
    motor.o(i.vOne_Step_Y) refers to board.o(.bss) for Usartdata
    motor.o(i.vTest_Sine_Wave) refers to printfa.o(i.__0printf) for __2printf
    motor.o(i.vTest_Sine_Wave) refers to motor.o(i.vDraw_Sine_Wave) for vDraw_Sine_Wave
    emm_v5.o(i.Emm_V5_En_Control) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Pos_Control) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Stop_Now) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_enQueue) for fifo_enQueue
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_deQueue) for fifo_deQueue
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for u_rxCount
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for u_rxCmd
    usart.o(i.usart_SendCmd) refers to usart.o(i.usart_SendByte) for usart_SendByte
    board.o(i.Camera_GetData) refers to board.o(.data) for camera_data
    board.o(i.Camera_HasNewData) refers to board.o(.data) for camera_data
    board.o(i.Camera_ParseData) refers to board.o(.data) for camera_data
    board.o(i.Key_Getlevel) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    board.o(i.Key_Getlevel) refers to delay.o(i.delay_ms) for delay_ms
    board.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    board.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    board.o(i.USART3_IRQHandler) refers to board.o(i.Camera_ParseData) for Camera_ParseData
    board.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    board.o(i.USART3_IRQHandler) refers to board.o(.data) for rx_index
    board.o(i.USART3_IRQHandler) refers to board.o(.bss) for rx_buffer
    board.o(i.board_init) refers to board.o(i.nvic_init) for nvic_init
    board.o(i.board_init) refers to board.o(i.clock_init) for clock_init
    board.o(i.board_init) refers to board.o(i.usart_init) for usart_init
    board.o(i.board_init) refers to board.o(i.vUsart_Init) for vUsart_Init
    board.o(i.board_init) refers to motor.o(i.vDriverInit) for vDriverInit
    board.o(i.board_init) refers to board.o(i.vKey_Init) for vKey_Init
    board.o(i.board_init) refers to board.o(i.vLED_Init) for vLED_Init
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.clock_init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    board.o(i.fputc) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    board.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    board.o(i.fputc) refers to returntozero.o(.data) for original_fputc_enabled
    board.o(i.nvic_init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    board.o(i.reset_receive_state) refers to board.o(.data) for rx_index
    board.o(i.usart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    board.o(i.vData_Get) refers to printfa.o(i.__0printf) for __2printf
    board.o(i.vData_Get) refers to board.o(i.vLED_Blink) for vLED_Blink
    board.o(i.vData_Get) refers to board.o(.data) for camera_data
    board.o(i.vData_Get) refers to board.o(.bss) for Usartdata
    board.o(i.vKey_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.vKey_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.vLED_Blink) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    board.o(i.vLED_Blink) refers to delay.o(i.delay_ms) for delay_ms
    board.o(i.vLED_Blink) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    board.o(i.vLED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.vLED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.vLED_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    board.o(i.vSingle_Point_Receive) refers to board.o(i.vData_Get) for vData_Get
    board.o(i.vSingle_Point_Receive) refers to delay.o(i.delay_ms) for delay_ms
    board.o(i.vSingle_Point_Receive) refers to board.o(.data) for usart_point
    board.o(i.vSingle_Point_Receive) refers to board.o(.bss) for Usartdata
    board.o(i.vUsart_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.vUsart_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.vUsart_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    board.o(i.vUsart_Init) refers to stm32f10x_usart.o(i.USART_StructInit) for USART_StructInit
    board.o(i.vUsart_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    board.o(i.vUsart_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    board.o(i.vUsart_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to motor.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to motor.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to motor.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to board.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    fifo.o(i.fifo_deQueue) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.fifo_enQueue) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.fifo_isEmpty) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.fifo_queueLength) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.initQueue) refers to fifo.o(.bss) for rxFIFO
    delay.o(i.delay_ms) refers to system_stm32f10x.o(.data) for SystemCoreClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    returntozero.o(i.disable_printf_output) refers to returntozero.o(.data) for original_fputc_enabled
    returntozero.o(i.enable_printf_output) refers to returntozero.o(.data) for original_fputc_enabled
    returntozero.o(i.vMotor_Disenable) refers to returntozero.o(i.disable_printf_output) for disable_printf_output
    returntozero.o(i.vMotor_Disenable) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    returntozero.o(i.vMotor_Disenable) refers to delay.o(i.delay_ms) for delay_ms
    returntozero.o(i.vMotor_Disenable) refers to emm_v5.o(i.Emm_V5_Synchronous_motion) for Emm_V5_Synchronous_motion
    returntozero.o(i.vMotor_Disenable) refers to returntozero.o(i.enable_printf_output) for enable_printf_output
    returntozero.o(i.vMotor_Enable) refers to returntozero.o(i.disable_printf_output) for disable_printf_output
    returntozero.o(i.vMotor_Enable) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    returntozero.o(i.vMotor_Enable) refers to delay.o(i.delay_ms) for delay_ms
    returntozero.o(i.vMotor_Enable) refers to emm_v5.o(i.Emm_V5_Synchronous_motion) for Emm_V5_Synchronous_motion
    returntozero.o(i.vMotor_Enable) refers to returntozero.o(i.enable_printf_output) for enable_printf_output
    returntozero.o(i.vReturn_To_Zero) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    returntozero.o(i.vReturn_To_Zero) refers to delay.o(i.delay_ms) for delay_ms
    returntozero.o(i.vReturn_To_Zero) refers to emm_v5.o(i.Emm_V5_Synchronous_motion) for Emm_V5_Synchronous_motion
    returntozero.o(i.vWait_Zero) refers to board.o(i.Key_Getlevel) for Key_Getlevel
    returntozero.o(i.vWait_Zero) refers to returntozero.o(i.disable_printf_output) for disable_printf_output
    returntozero.o(i.vWait_Zero) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    returntozero.o(i.vWait_Zero) refers to delay.o(i.delay_ms) for delay_ms
    returntozero.o(i.vWait_Zero) refers to returntozero.o(i.enable_printf_output) for enable_printf_output
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.sin) for sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.__sin$lsc) for __sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sin_x.o(i.__sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.__sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.__sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.__sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to board.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to board.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to board.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to board.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to board.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to board.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to board.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to board.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to board.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to board.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to board.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to board.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to board.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to board.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to board.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to board.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to board.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to board.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to board.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to board.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to board.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to board.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to board.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to board.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to board.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to board.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to board.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to board.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to board.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to board.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to board.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to board.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to board.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to board.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to board.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to board.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to board.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to board.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to board.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to board.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to board.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to board.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to board.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to board.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to dscalb.o(.text) for __ARM_scalbn
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to dscalb.o(.text) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dscalb.o(.text) for __ARM_scalbn
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing motor.o(i.Delayms), (34 bytes).
    Removing motor.o(i.Motor_Timer_init), (80 bytes).
    Removing motor.o(i.PID_Control), (130 bytes).
    Removing motor.o(i.Pwm_UIE_init), (92 bytes).
    Removing motor.o(i.vDraw_Sine_Wave), (212 bytes).
    Removing motor.o(i.vGenerate_Sine_Wave), (692 bytes).
    Removing motor.o(i.vImage_To_Actual), (332 bytes).
    Removing motor.o(i.vInterpolation_Move), (72 bytes).
    Removing motor.o(i.vInterpolation_Point), (416 bytes).
    Removing motor.o(i.vOne_Step_X), (76 bytes).
    Removing motor.o(i.vOne_Step_Y), (76 bytes).
    Removing motor.o(i.vTest_Sine_Wave), (208 bytes).
    Removing motor.o(.bss), (208 bytes).
    Removing motor.o(.data), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_En_Control), (58 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (58 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (170 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Pos_Control), (108 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (262 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Stop_Now), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (78 bytes).
    Removing usart.o(i.usart_SendByte), (60 bytes).
    Removing usart.o(i.usart_SendCmd), (44 bytes).
    Removing board.o(i.Camera_GetData), (8 bytes).
    Removing board.o(i.Camera_HasNewData), (24 bytes).
    Removing board.o(i.Key_Getlevel), (52 bytes).
    Removing board.o(i.reset_receive_state), (20 bytes).
    Removing board.o(i.vSingle_Point_Receive), (84 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing fifo.o(i.fifo_isEmpty), (28 bytes).
    Removing fifo.o(i.initQueue), (20 bytes).
    Removing delay.o(i.delay_cnt), (10 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing returntozero.o(i.disable_printf_output), (12 bytes).
    Removing returntozero.o(i.enable_printf_output), (12 bytes).
    Removing returntozero.o(i.vMotor_Disenable), (62 bytes).
    Removing returntozero.o(i.vMotor_Enable), (56 bytes).
    Removing returntozero.o(i.vReturn_To_Zero), (70 bytes).
    Removing returntozero.o(i.vWait_Zero), (50 bytes).
    Removing returntozero.o(.bss), (128 bytes).
    Removing fadd.o(.text), (176 bytes).
    Removing fmul.o(.text), (100 bytes).
    Removing fdiv.o(.text), (124 bytes).
    Removing fflti.o(.text), (18 bytes).
    Removing ffltui.o(.text), (10 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing f2d.o(.text), (38 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing cfrcmple.o(.text), (20 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixi.o(.text), (62 bytes).

219 unused section(s) (total 11736 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ..\APP\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\APP\stm32f10x_it.c                    0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\BSP\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\BSP\board.c                           0x00000000   Number         0  board.o ABSOLUTE
    ..\BSP\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\Code\ReturnToZero.c                   0x00000000   Number         0  returntozero.o ABSOLUTE
    ..\Code\motor.c                          0x00000000   Number         0  motor.o ABSOLUTE
    ..\DRIVERS\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    ..\DRIVERS\fifo.c                        0x00000000   Number         0  fifo.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_tim.c 0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000140   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000144   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000144   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000144   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000144   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000148   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800016c   Section        0  dmul.o(.text)
    .text                                    0x08000250   Section        0  uidiv.o(.text)
    .text                                    0x0800027c   Section        0  uldiv.o(.text)
    .text                                    0x080002de   Section        0  depilogue.o(.text)
    .text                                    0x080002de   Section        0  iusefp.o(.text)
    .text                                    0x08000398   Section        0  dadd.o(.text)
    .text                                    0x080004e6   Section        0  ddiv.o(.text)
    .text                                    0x080005c4   Section        0  dfixul.o(.text)
    .text                                    0x080005f4   Section       48  cdrcmple.o(.text)
    .text                                    0x08000624   Section       36  init.o(.text)
    .text                                    0x08000648   Section        0  llshl.o(.text)
    .text                                    0x08000666   Section        0  llushr.o(.text)
    .text                                    0x08000686   Section        0  llsshr.o(.text)
    i.BusFault_Handler                       0x080006aa   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Camera_ParseData                       0x080006b0   Section        0  board.o(i.Camera_ParseData)
    i.DebugMon_Handler                       0x080006fc   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080006fe   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x08000814   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GPIO_ResetBits                         0x080008a4   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080008a8   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x080008ac   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080008b0   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080008b4   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080008b8   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000928   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x0800093c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08000940   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000960   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000980   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08000a54   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08000a56   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08000a57   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000a60   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000a61   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000b40   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000b44   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08000ba4   Section        0  motor.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08000bbc   Section        0  motor.o(i.TIM3_IRQHandler)
    i.TIM4_IRQHandler                        0x08000c18   Section        0  motor.o(i.TIM4_IRQHandler)
    i.USART1_IRQHandler                      0x08000c74   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART3_IRQHandler                      0x08000cfc   Section        0  board.o(i.USART3_IRQHandler)
    i.USART_ClearITPendingBit                0x08000db0   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08000dce   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08000de6   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08000e00   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08000e54   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08000ea0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08000f78   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08000f82   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.USART_StructInit                       0x08000f8a   Section        0  stm32f10x_usart.o(i.USART_StructInit)
    i.UsageFault_Handler                     0x08000fa2   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08000fa8   Section        0  printfa.o(i.__0printf)
    i.__scatterload_copy                     0x08000fc8   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08000fd6   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08000fd8   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08000fe8   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08000fe9   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0800116c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800116d   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08001820   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08001821   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08001844   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08001845   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.board_init                             0x08001872   Section        0  board.o(i.board_init)
    i.clock_init                             0x08001894   Section        0  board.o(i.clock_init)
    i.delay_ms                               0x080018b4   Section        0  delay.o(i.delay_ms)
    i.fifo_deQueue                           0x08001914   Section        0  fifo.o(i.fifo_deQueue)
    i.fifo_enQueue                           0x08001944   Section        0  fifo.o(i.fifo_enQueue)
    i.fifo_queueLength                       0x08001974   Section        0  fifo.o(i.fifo_queueLength)
    i.fputc                                  0x080019b0   Section        0  board.o(i.fputc)
    i.main                                   0x080019e4   Section        0  main.o(i.main)
    i.nvic_init                              0x08001a84   Section        0  board.o(i.nvic_init)
    i.usart_init                             0x08001ac8   Section        0  board.o(i.usart_init)
    i.vData_Get                              0x08001b64   Section        0  board.o(i.vData_Get)
    i.vDriverInit                            0x08001bd8   Section        0  motor.o(i.vDriverInit)
    i.vKey_Init                              0x08001c64   Section        0  board.o(i.vKey_Init)
    i.vLED_Blink                             0x08001c90   Section        0  board.o(i.vLED_Blink)
    i.vLED_Init                              0x08001cc4   Section        0  board.o(i.vLED_Init)
    i.vUsart_Init                            0x08001cf4   Section        0  board.o(i.vUsart_Init)
    .data                                    0x20000000   Section        4  main.o(.data)
    heartbeat_counter                        0x20000000   Data           4  main.o(.data)
    .data                                    0x20000004   Section        2  usart.o(.data)
    .data                                    0x20000006   Section       20  board.o(.data)
    rx_index                                 0x20000006   Data           1  board.o(.data)
    camera_data                              0x20000008   Data           8  board.o(.data)
    .data                                    0x2000001c   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000030   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000030   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000040   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000044   Section        8  returntozero.o(.data)
    .data                                    0x2000004c   Section        4  stdout.o(.data)
    .bss                                     0x20000050   Section      128  usart.o(.bss)
    .bss                                     0x200000d0   Section      156  board.o(.bss)
    rx_buffer                                0x200000d0   Data          64  board.o(.bss)
    .bss                                     0x2000016c   Section      258  fifo.o(.bss)
    STACK                                    0x20000270   Section     2048  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000141   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000145   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000145   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000149   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08000163   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_dmul                             0x0800016d   Thumb Code   228  dmul.o(.text)
    __aeabi_uidiv                            0x08000251   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000251   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800027d   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x080002df   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x080002df   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080002fd   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000399   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080004db   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080004e1   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x080004e7   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x080005c5   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080005f5   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000625   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000625   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000649   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000649   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000667   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000667   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000687   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000687   Thumb Code     0  llsshr.o(.text)
    BusFault_Handler                         0x080006ab   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Camera_ParseData                         0x080006b1   Thumb Code    72  board.o(i.Camera_ParseData)
    DebugMon_Handler                         0x080006fd   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080006ff   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x08000815   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GPIO_ResetBits                           0x080008a5   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080008a9   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x080008ad   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080008b1   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080008b5   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x080008b9   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000929   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x0800093d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08000941   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000961   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000981   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08000a55   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000b41   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08000b45   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08000ba5   Thumb Code    24  motor.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08000bbd   Thumb Code    80  motor.o(i.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x08000c19   Thumb Code    80  motor.o(i.TIM4_IRQHandler)
    USART1_IRQHandler                        0x08000c75   Thumb Code   120  usart.o(i.USART1_IRQHandler)
    USART3_IRQHandler                        0x08000cfd   Thumb Code   166  board.o(i.USART3_IRQHandler)
    USART_ClearITPendingBit                  0x08000db1   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08000dcf   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08000de7   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08000e01   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08000e55   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08000ea1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08000f79   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08000f83   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    USART_StructInit                         0x08000f8b   Thumb Code    24  stm32f10x_usart.o(i.USART_StructInit)
    UsageFault_Handler                       0x08000fa3   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __0printf                                0x08000fa9   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08000fa9   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08000fa9   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08000fa9   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08000fa9   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x08000fc9   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08000fd7   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08000fd9   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    board_init                               0x08001873   Thumb Code    32  board.o(i.board_init)
    clock_init                               0x08001895   Thumb Code    28  board.o(i.clock_init)
    delay_ms                                 0x080018b5   Thumb Code    90  delay.o(i.delay_ms)
    fifo_deQueue                             0x08001915   Thumb Code    44  fifo.o(i.fifo_deQueue)
    fifo_enQueue                             0x08001945   Thumb Code    42  fifo.o(i.fifo_enQueue)
    fifo_queueLength                         0x08001975   Thumb Code    56  fifo.o(i.fifo_queueLength)
    fputc                                    0x080019b1   Thumb Code    42  board.o(i.fputc)
    main                                     0x080019e5   Thumb Code    64  main.o(i.main)
    nvic_init                                0x08001a85   Thumb Code    68  board.o(i.nvic_init)
    usart_init                               0x08001ac9   Thumb Code   148  board.o(i.usart_init)
    vData_Get                                0x08001b65   Thumb Code    74  board.o(i.vData_Get)
    vDriverInit                              0x08001bd9   Thumb Code   114  motor.o(i.vDriverInit)
    vKey_Init                                0x08001c65   Thumb Code    38  board.o(i.vKey_Init)
    vLED_Blink                               0x08001c91   Thumb Code    46  board.o(i.vLED_Blink)
    vLED_Init                                0x08001cc5   Thumb Code    44  board.o(i.vLED_Init)
    vUsart_Init                              0x08001cf5   Thumb Code   146  board.o(i.vUsart_Init)
    Region$$Table$$Base                      0x08001d90   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001db0   Number         0  anon$$obj.o(Region$$Table)
    rxFrameFlag                              0x20000004   Data           1  usart.o(.data)
    u_rxCount                                0x20000005   Data           1  usart.o(.data)
    Receive_Finish                           0x20000010   Data           1  board.o(.data)
    Receive_Flag                             0x20000011   Data           1  board.o(.data)
    Receive_state                            0x20000012   Data           1  board.o(.data)
    XendYstart                               0x20000013   Data           1  board.o(.data)
    Numstart                                 0x20000014   Data           1  board.o(.data)
    usart_point                              0x20000016   Data           4  board.o(.data)
    SystemCoreClock                          0x2000001c   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000020   Data          16  system_stm32f10x.o(.data)
    rxCount                                  0x20000044   Data           1  returntozero.o(.data)
    original_fputc_enabled                   0x20000048   Data           4  returntozero.o(.data)
    __stdout                                 0x2000004c   Data           4  stdout.o(.data)
    u_rxCmd                                  0x20000050   Data         128  usart.o(.bss)
    Receive_String                           0x20000110   Data          64  board.o(.bss)
    Usartdata                                0x20000150   Data          28  board.o(.bss)
    rxFIFO                                   0x2000016c   Data         258  fifo.o(.bss)
    __initial_sp                             0x20000a70   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001e00, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001db0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          519    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         1762  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         2089    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         2092    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         2094    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         2096    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         2097    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         2104    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000144   0x08000144   0x00000000   Code   RO         2099    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000144   0x08000144   0x00000000   Code   RO         2101    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000144   0x08000144   0x00000004   Code   RO         2090    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000148   0x08000148   0x00000024   Code   RO          520    .text               startup_stm32f10x_hd.o
    0x0800016c   0x0800016c   0x000000e4   Code   RO         2034    .text               mf_w.l(dmul.o)
    0x08000250   0x08000250   0x0000002c   Code   RO         2106    .text               mc_w.l(uidiv.o)
    0x0800027c   0x0800027c   0x00000062   Code   RO         2108    .text               mc_w.l(uldiv.o)
    0x080002de   0x080002de   0x00000000   Code   RO         2117    .text               mc_w.l(iusefp.o)
    0x080002de   0x080002de   0x000000ba   Code   RO         2120    .text               mf_w.l(depilogue.o)
    0x08000398   0x08000398   0x0000014e   Code   RO         2122    .text               mf_w.l(dadd.o)
    0x080004e6   0x080004e6   0x000000de   Code   RO         2124    .text               mf_w.l(ddiv.o)
    0x080005c4   0x080005c4   0x00000030   Code   RO         2126    .text               mf_w.l(dfixul.o)
    0x080005f4   0x080005f4   0x00000030   Code   RO         2130    .text               mf_w.l(cdrcmple.o)
    0x08000624   0x08000624   0x00000024   Code   RO         2136    .text               mc_w.l(init.o)
    0x08000648   0x08000648   0x0000001e   Code   RO         2138    .text               mc_w.l(llshl.o)
    0x08000666   0x08000666   0x00000020   Code   RO         2140    .text               mc_w.l(llushr.o)
    0x08000686   0x08000686   0x00000024   Code   RO         2142    .text               mc_w.l(llsshr.o)
    0x080006aa   0x080006aa   0x00000004   Code   RO           77    i.BusFault_Handler  stm32f10x_it.o
    0x080006ae   0x080006ae   0x00000002   PAD
    0x080006b0   0x080006b0   0x0000004c   Code   RO          385    i.Camera_ParseData  board.o
    0x080006fc   0x080006fc   0x00000002   Code   RO           78    i.DebugMon_Handler  stm32f10x_it.o
    0x080006fe   0x080006fe   0x00000116   Code   RO          624    i.GPIO_Init         stm32f10x_gpio.o
    0x08000814   0x08000814   0x00000090   Code   RO          626    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x080008a4   0x080008a4   0x00000004   Code   RO          631    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080008a8   0x080008a8   0x00000004   Code   RO          632    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080008ac   0x080008ac   0x00000004   Code   RO           79    i.HardFault_Handler  stm32f10x_it.o
    0x080008b0   0x080008b0   0x00000004   Code   RO           80    i.MemManage_Handler  stm32f10x_it.o
    0x080008b4   0x080008b4   0x00000002   Code   RO           81    i.NMI_Handler       stm32f10x_it.o
    0x080008b6   0x080008b6   0x00000002   PAD
    0x080008b8   0x080008b8   0x00000070   Code   RO         1112    i.NVIC_Init         misc.o
    0x08000928   0x08000928   0x00000014   Code   RO         1113    i.NVIC_PriorityGroupConfig  misc.o
    0x0800093c   0x0800093c   0x00000002   Code   RO           82    i.PendSV_Handler    stm32f10x_it.o
    0x0800093e   0x0800093e   0x00000002   PAD
    0x08000940   0x08000940   0x00000020   Code   RO          734    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000960   0x08000960   0x00000020   Code   RO          736    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000980   0x08000980   0x000000d4   Code   RO          744    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000a54   0x08000a54   0x00000002   Code   RO           83    i.SVC_Handler       stm32f10x_it.o
    0x08000a56   0x08000a56   0x00000008   Code   RO          524    i.SetSysClock       system_stm32f10x.o
    0x08000a5e   0x08000a5e   0x00000002   PAD
    0x08000a60   0x08000a60   0x000000e0   Code   RO          525    i.SetSysClockTo72   system_stm32f10x.o
    0x08000b40   0x08000b40   0x00000002   Code   RO           84    i.SysTick_Handler   stm32f10x_it.o
    0x08000b42   0x08000b42   0x00000002   PAD
    0x08000b44   0x08000b44   0x00000060   Code   RO          527    i.SystemInit        system_stm32f10x.o
    0x08000ba4   0x08000ba4   0x00000018   Code   RO          144    i.TIM2_IRQHandler   motor.o
    0x08000bbc   0x08000bbc   0x0000005c   Code   RO          145    i.TIM3_IRQHandler   motor.o
    0x08000c18   0x08000c18   0x0000005c   Code   RO          146    i.TIM4_IRQHandler   motor.o
    0x08000c74   0x08000c74   0x00000088   Code   RO          348    i.USART1_IRQHandler  usart.o
    0x08000cfc   0x08000cfc   0x000000b4   Code   RO          387    i.USART3_IRQHandler  board.o
    0x08000db0   0x08000db0   0x0000001e   Code   RO          933    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08000dce   0x08000dce   0x00000018   Code   RO          936    i.USART_Cmd         stm32f10x_usart.o
    0x08000de6   0x08000de6   0x0000001a   Code   RO          939    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08000e00   0x08000e00   0x00000054   Code   RO          940    i.USART_GetITStatus  stm32f10x_usart.o
    0x08000e54   0x08000e54   0x0000004a   Code   RO          942    i.USART_ITConfig    stm32f10x_usart.o
    0x08000e9e   0x08000e9e   0x00000002   PAD
    0x08000ea0   0x08000ea0   0x000000d8   Code   RO          943    i.USART_Init        stm32f10x_usart.o
    0x08000f78   0x08000f78   0x0000000a   Code   RO          950    i.USART_ReceiveData  stm32f10x_usart.o
    0x08000f82   0x08000f82   0x00000008   Code   RO          953    i.USART_SendData    stm32f10x_usart.o
    0x08000f8a   0x08000f8a   0x00000018   Code   RO          959    i.USART_StructInit  stm32f10x_usart.o
    0x08000fa2   0x08000fa2   0x00000004   Code   RO           85    i.UsageFault_Handler  stm32f10x_it.o
    0x08000fa6   0x08000fa6   0x00000002   PAD
    0x08000fa8   0x08000fa8   0x00000020   Code   RO         2000    i.__0printf         mc_w.l(printfa.o)
    0x08000fc8   0x08000fc8   0x0000000e   Code   RO         2152    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08000fd6   0x08000fd6   0x00000002   Code   RO         2153    i.__scatterload_null  mc_w.l(handlers.o)
    0x08000fd8   0x08000fd8   0x0000000e   Code   RO         2154    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08000fe6   0x08000fe6   0x00000002   PAD
    0x08000fe8   0x08000fe8   0x00000184   Code   RO         2007    i._fp_digits        mc_w.l(printfa.o)
    0x0800116c   0x0800116c   0x000006b4   Code   RO         2008    i._printf_core      mc_w.l(printfa.o)
    0x08001820   0x08001820   0x00000024   Code   RO         2009    i._printf_post_padding  mc_w.l(printfa.o)
    0x08001844   0x08001844   0x0000002e   Code   RO         2010    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08001872   0x08001872   0x00000020   Code   RO          388    i.board_init        board.o
    0x08001892   0x08001892   0x00000002   PAD
    0x08001894   0x08001894   0x00000020   Code   RO          389    i.clock_init        board.o
    0x080018b4   0x080018b4   0x00000060   Code   RO          601    i.delay_ms          delay.o
    0x08001914   0x08001914   0x00000030   Code   RO          558    i.fifo_deQueue      fifo.o
    0x08001944   0x08001944   0x00000030   Code   RO          559    i.fifo_enQueue      fifo.o
    0x08001974   0x08001974   0x0000003c   Code   RO          561    i.fifo_queueLength  fifo.o
    0x080019b0   0x080019b0   0x00000034   Code   RO          390    i.fputc             board.o
    0x080019e4   0x080019e4   0x000000a0   Code   RO            1    i.main              main.o
    0x08001a84   0x08001a84   0x00000044   Code   RO          391    i.nvic_init         board.o
    0x08001ac8   0x08001ac8   0x0000009c   Code   RO          393    i.usart_init        board.o
    0x08001b64   0x08001b64   0x00000074   Code   RO          394    i.vData_Get         board.o
    0x08001bd8   0x08001bd8   0x0000008c   Code   RO          148    i.vDriverInit       motor.o
    0x08001c64   0x08001c64   0x0000002c   Code   RO          395    i.vKey_Init         board.o
    0x08001c90   0x08001c90   0x00000034   Code   RO          396    i.vLED_Blink        board.o
    0x08001cc4   0x08001cc4   0x00000030   Code   RO          397    i.vLED_Init         board.o
    0x08001cf4   0x08001cf4   0x0000009c   Code   RO          399    i.vUsart_Init       board.o
    0x08001d90   0x08001d90   0x00000020   Data   RO         2150    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001db0, Size: 0x00000a70, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001db0   0x00000004   Data   RW            2    .data               main.o
    0x20000004   0x08001db4   0x00000002   Data   RW          352    .data               usart.o
    0x20000006   0x08001db6   0x00000014   Data   RW          401    .data               board.o
    0x2000001a   0x08001dca   0x00000002   PAD
    0x2000001c   0x08001dcc   0x00000014   Data   RW          528    .data               system_stm32f10x.o
    0x20000030   0x08001de0   0x00000014   Data   RW          764    .data               stm32f10x_rcc.o
    0x20000044   0x08001df4   0x00000008   Data   RW         1703    .data               returntozero.o
    0x2000004c   0x08001dfc   0x00000004   Data   RW         2105    .data               mc_w.l(stdout.o)
    0x20000050        -       0x00000080   Zero   RW          351    .bss                usart.o
    0x200000d0        -       0x0000009c   Zero   RW          400    .bss                board.o
    0x2000016c        -       0x00000102   Zero   RW          563    .bss                fifo.o
    0x2000026e   0x08001e00   0x00000002   PAD
    0x20000270        -       0x00000800   Zero   RW          517    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1012        108          0         20        156       9599   board.o
         0          0          0          0          0         32   core_cm3.o
        96          6          0          0          0        927   delay.o
       156         14          0          0        258       2394   fifo.o
       160         96          0          4          0     231651   main.o
       132         22          0          0          0       1771   misc.o
       348         50          0          0          0       1812   motor.o
         0          0          0          8          0        739   returntozero.o
        36          8        304          0       2048        788   startup_stm32f10x_hd.o
       430          6          0          0          0       4166   stm32f10x_gpio.o
        26          0          0          0          0       3790   stm32f10x_it.o
       276         32          0         20          0       4710   stm32f10x_rcc.o
       496          6          0          0          0       7790   stm32f10x_usart.o
       328         28          0         20          0       2265   system_stm32f10x.o
       136         16          0          2        128       1244   usart.o

    ----------------------------------------------------------------------
      3648        <USER>        <GROUP>         76       2592     273678   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          0          2          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2218         90          0          0          0        464   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3616        <USER>          <GROUP>          4          0       1564   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2548        106          0          4          0        908   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3616        <USER>          <GROUP>          4          0       1564   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7264        498        336         80       2592     272066   Grand Totals
      7264        498        336         80       2592     272066   ELF Image Totals
      7264        498        336         80          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7600 (   7.42kB)
    Total RW  Size (RW Data + ZI Data)              2672 (   2.61kB)
    Total ROM Size (Code + RO Data + RW Data)       7680 (   7.50kB)

==============================================================================

