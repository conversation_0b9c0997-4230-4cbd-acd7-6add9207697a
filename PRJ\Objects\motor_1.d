.\objects\motor_1.o: ..\Code\motor.c
.\objects\motor_1.o: ..\Code\motor.h
.\objects\motor_1.o: ..\CMSIS\stm32f10x.h
.\objects\motor_1.o: ..\CMSIS\core_cm3.h
.\objects\motor_1.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\motor_1.o: ..\CMSIS\system_stm32f10x.h
.\objects\motor_1.o: ..\APP\stm32f10x_conf.h
.\objects\motor_1.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\motor_1.o: ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_gpio.h
.\objects\motor_1.o: ..\CMSIS\stm32f10x.h
.\objects\motor_1.o: ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_rcc.h
.\objects\motor_1.o: ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_tim.h
.\objects\motor_1.o: ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\stm32f10x_usart.h
.\objects\motor_1.o: ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\inc\misc.h
.\objects\motor_1.o: ..\BSP\ZDT_X42_V2.h
.\objects\motor_1.o: ..\BSP\usart.h
.\objects\motor_1.o: ..\BSP\board.h
.\objects\motor_1.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\motor_1.o: ..\Code\motor.h
.\objects\motor_1.o: ..\DRIVERS\fifo.h
.\objects\motor_1.o: ..\DRIVERS\delay.h
.\objects\motor_1.o: D:\keil5\ARM\ARMCC\Bin\..\include\math.h
.\objects\motor_1.o: ..\Code\ReturnToZero.h
